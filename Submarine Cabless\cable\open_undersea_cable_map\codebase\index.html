<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="sidebar">
        <div class="app-header">
            <div class="app-logo">
                <div class="logo-icon"></div>
                <h1 class="app-title">Submarine CableMap</h1>
                <p class="app-subtitle">Global Undersea Cable Network Explorer</p>
            </div>
        </div>

        <div class="search-section">
            <div class="search-group">
                <label for="fromCountry">From Country:</label>
                <input type="text" id="fromCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="fromCountryDropdown" class="dropdown-list"></div>
            </div>

            <div class="search-group">
                <label for="toCountry">To Country:</label>
                <input type="text" id="toCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="toCountryDropdown" class="dropdown-list"></div>
            </div>

            <div class="search-group">
                <label for="searchType">Search Type:</label>
                <select id="searchType" class="search-input">
                    <option value="direct">Direct Connection (1-way)</option>
                    <option value="multi-hop">Multi-hop Route (2-way)</option>
                    <option value="both" selected>Both Direct & Multi-hop</option>
                </select>
            </div>



            <div class="button-group">
                <button id="searchBtn" class="btn btn-primary" disabled>Search Cables</button>
                <button id="clearBtn" class="btn btn-secondary">Clear Search</button>
            </div>
        </div>

        <div class="highlighting-section">
            <div class="section-header">Landing Point Highlighting</div>

            <div class="highlight-options">
                <div class="highlight-option">
                    <label class="checkbox-label">
                        <input type="checkbox" id="highlightTerminals" class="highlight-checkbox">
                        <span class="checkmark"></span>
                        Highlight Terminal Landing Points
                    </label>
                    <div class="option-description">Highlights the start and end landing points of cable routes</div>
                </div>

                <div class="highlight-option">
                    <label for="highlightCountry">Highlight Country Landing Points:</label>
                    <input type="text" id="highlightCountry" class="search-input" placeholder="Select country to highlight..." autocomplete="off">
                    <div id="highlightCountryDropdown" class="dropdown-list"></div>
                    <div class="option-description">Highlights landing points for a specific country</div>
                </div>
            </div>
        </div>


        <div class="results-section">
            <div id="resultsHeader" class="results-header" style="display: none;">Found Cables:</div>
            <div id="cableResults"></div>
        </div>
       
    </div>

    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="./script.js"></script>
</body>
</html>

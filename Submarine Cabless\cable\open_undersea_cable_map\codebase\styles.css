body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #map {
            height: 100vh;
            width: calc(100% - 350px); 
            margin-left: 350px;
            position: relative;
        }

        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 350px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        #sidebar h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }

        .app-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
            position: relative;
            overflow: hidden;
        }

        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .app-logo {
            position: relative;
            z-index: 2;
        }

        .logo-icon {
            display: inline-block;
            width: 48px;
            height: 48px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 50%;
            margin-bottom: 12px;
            position: relative;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        /* Alternative: Custom Image Logo */
        .logo-icon.custom-image {
            background-image: url('your-logo.png');
            background-size: 70%;
            background-position: center;
            background-repeat: no-repeat;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .logo-icon.custom-image::before,
        .logo-icon.custom-image::after {
            display: none; 
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 28px;
            height: 4px;
            background: linear-gradient(90deg, #fff 0%, #e8f4fd 50%, #fff 100%);
            border-radius: 2px;
            box-shadow:
                0 -8px 0 -1px rgba(255, 255, 255, 0.8),
                0 8px 0 -1px rgba(255, 255, 255, 0.8),
                0 -16px 0 -2px rgba(255, 255, 255, 0.6),
                0 16px 0 -2px rgba(255, 255, 255, 0.6);
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            background: #fff;
            border-radius: 50%;
            box-shadow:
                -10px 0 0 -1px rgba(255, 255, 255, 0.9),
                10px 0 0 -1px rgba(255, 255, 255, 0.9);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .app-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            font-weight: 400;
            margin: 4px 0 0 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .search-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8e8e8;
        }

        .search-group {
            margin-bottom: 15px;
        }

        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
            font-size: 14px;
        }

        .search-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        .dropdown-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .search-group {
            position: relative;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-primary:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .results-section {
            margin-top: 20px;
        }

        /* Landing Point Highlighting Section */
        .highlighting-section {
            margin-top: 20px;
            padding: 15px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .section-header {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px solid #dee2e6;
        }

        .highlight-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .highlight-option {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            color: #495057;
            user-select: none;
        }

        .highlight-checkbox {
            margin-right: 8px;
            cursor: pointer;
        }

        .option-description {
            font-size: 10px;
            color: #6c757d;
            font-style: italic;
            margin-left: 20px;
        }

        .results-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .cable-result {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease, transform 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .cable-result:hover {
            border-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .cable-result:active {
            transform: translateY(0px) scale(0.98);
        }

        .cable-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 14px;
            display: flex;
            align-items: center;
            line-height: 1.3;
        }

        .cable-details {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .cable-landing-points {
            font-size: 11px;
            color: #5a6c7d;
            margin-top: 6px;
            padding-top: 6px;
            border-top: 1px solid #ecf0f1;
            line-height: 1.3;
        }

        .landing-points-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 2px;
        }

        .landing-points-route {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 4px;
        }

        .landing-point {
            background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
            white-space: nowrap;
            transition: all 0.2s ease;
        }

        /* Terminal landing point highlighting */
        .landing-point-terminal {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: 1px solid #2980b9;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
        }

        /* Country-specific highlighting */
        .landing-point-country-highlight {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: 1px solid #c0392b;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
        }

        /* Combined highlighting (terminal + country) */
        .landing-point-terminal.landing-point-country-highlight {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            border: 1px solid #8e44ad;
            box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3);
        }

        .route-arrow {
            color: #3498db;
            font-weight: bold;
            font-size: 10px;
            margin: 0 2px;
        }

        .cable-color-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            vertical-align: middle;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            flex-shrink: 0; /* Prevent shrinking in flex layouts */
        }

        .no-results {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }

        .segment-header {
            font-size: 12px;
            font-weight: 600;
            color: #2c3e50;
            background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%);
            padding: 8px 12px;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #bdc3c7;
        }

        .segment-cables {
            margin-left: 8px;
            border-left: 2px solid #ecf0f1;
            padding-left: 12px;
        }

        .segment-cables .cable-result {
            margin-bottom: 8px;
        }

        .segment-cables .cable-result:last-child {
            margin-bottom: 0;
        }

        .info {
            padding: 6px 8px;
            font: 14px/16px Arial, Helvetica, sans-serif;
            background: white;
            background: rgba(255,255,255,0.8);
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            border-radius: 5px;
        }
        .info h4 {
            margin: 0 0 5px;
            color: #777;
        }

        /* Responsive design for mobile devices */
        @media (max-width: 768px) {
            #sidebar {
                width: 300px; 
            }

            #map {
                width: calc(100% - 300px);
                margin-left: 300px;
            }

            .cable-name-display {
                font-size: 12px;
                padding: 6px 10px;
                max-width: 250px;
            }

            .cable-color-indicator {
                width: 14px;
                height: 14px;
                margin-right: 8px;
            }

            .app-header {
                padding: 16px;
                margin-bottom: 20px;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                margin-bottom: 10px;
            }

            .logo-icon::before {
                font-size: 20px;
            }

            .app-title {
                font-size: 16px;
            }

            .app-subtitle {
                font-size: 11px;
            }

            .cable-landing-points {
                font-size: 10px;
                margin-top: 4px;
                padding-top: 4px;
            }

            .landing-point {
                font-size: 9px;
                padding: 1px 4px;
            }

            .highlighting-section {
                margin-top: 15px;
                padding: 12px;
            }

            .section-header {
                font-size: 12px;
                margin-bottom: 10px;
            }

            .checkbox-label {
                font-size: 11px;
            }

            .option-description {
                font-size: 9px;
                margin-left: 16px;
            }

            .route-arrow {
                font-size: 9px;
            }
        }

        @media (max-width: 480px) {
            body {
                display: flex;
                flex-direction: column;
                height: 100vh;
                margin: 0;
                padding: 0;
            }

            #map {
                width: 100%;
                margin-left: 0;
                height: 65vh; /* Map takes top space */
                order: 1;
            }

            #sidebar {
                width: 100%;
                height: 35vh; /* Sidebar at bottom on mobile */
                position: relative;
                border-right: none;
                border-top: 1px solid #e0e0e0;
                border-bottom: none;
                order: 2;
            }

            .search-section {
                margin-bottom: 15px;
                padding-bottom: 15px;
            }

            .search-group {
                margin-bottom: 10px;
            }

            .cable-name-display {
                font-size: 11px;
                padding: 5px 8px;
                max-width: 200px;
            }

            .cable-color-indicator {
                width: 12px;
                height: 12px;
                margin-right: 6px;
                border-width: 1px;
            }

            .cable-name {
                font-size: 13px;
            }

            .app-header {
                padding: 12px;
                margin-bottom: 15px;
                border-radius: 8px;
            }

            .logo-icon {
                width: 36px;
                height: 36px;
                margin-bottom: 8px;
            }

            .logo-icon::before {
                font-size: 18px;
            }

            .app-title {
                font-size: 15px;
            }

            .app-subtitle {
                font-size: 10px;
            }

            .cable-landing-points {
                font-size: 9px;
                margin-top: 3px;
                padding-top: 3px;
            }

            .landing-point {
                font-size: 8px;
                padding: 1px 3px;
                margin: 1px;
            }

            .route-arrow {
                font-size: 8px;
                margin: 0 1px;
            }

            .landing-points-route {
                gap: 2px;
            }

            .segment-header {
                font-size: 10px;
                padding: 6px 8px;
                margin-bottom: 6px;
            }

            .segment-cables {
                margin-left: 6px;
                padding-left: 8px;
            }

            .highlighting-section {
                margin-top: 12px;
                padding: 10px;
            }

            .section-header {
                font-size: 11px;
                margin-bottom: 8px;
            }

            .checkbox-label {
                font-size: 10px;
            }

            .option-description {
                font-size: 8px;
                margin-left: 14px;
            }
        }

        /* Cable Name Label Popup Styling */
        .cable-name-popup .leaflet-popup-content-wrapper {
            background: white;
            border-radius: 6px;
            box-shadow: 0 3px 14px rgba(0,0,0,0.4);
        }

        .cable-name-popup .leaflet-popup-content {
            margin: 8px 12px;
            line-height: 1.4;
        }

        .cable-name-popup .leaflet-popup-tip {
            background: white;
        }

        /* Multi-hop routing styles */
        .search-results-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .results-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .results-icon {
            font-size: 18px;
        }

        .results-route {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .country-tag {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
        }

        .route-arrow {
            color: #3498db;
            font-weight: bold;
            font-size: 16px;
        }

        .path-section {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .path-header {
            background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
            padding: 12px 15px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #2c3e50;
        }

        .path-type-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .path-type-direct {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
        }

        .path-type-multi-hop {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.2);
        }

        .path-route {
            padding: 12px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e8e8e8;
        }

        .path-route-label {
            font-size: 12px;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .path-countries {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: wrap;
        }

        .path-country {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid #ced4da;
        }

        .path-country.highlight {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border-color: #2980b9;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
        }

        .path-arrow {
            color: #6c757d;
            font-weight: bold;
            font-size: 14px;
        }

        .no-results {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .no-results-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .no-results-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #495057;
        }

        .no-results-suggestion {
            font-size: 14px;
            color: #6c757d;
            font-style: italic;
        }

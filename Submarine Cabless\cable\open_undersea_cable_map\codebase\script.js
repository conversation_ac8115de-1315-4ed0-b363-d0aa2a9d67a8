
        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0,
            minZoom: 2,
            maxZoom: 8
        }).setView([20, 0], 2);

        <PERSON><PERSON>tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Create info control for hover information
        const info = L.control();
        info.onAdd = function (map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function (props) {
            this._div.innerHTML = '<h4>Submarine Cable Info</h4>' +  (props ?
                '<b>' + props.name + '</b><br />' +
                (props.rfs ? 'RFS: ' + props.rfs + '<br />' : '') +
                (props.length ? 'Length: ' + props.length + ' km<br />' : '') +
                (props.owners ? 'Owners: ' + props.owners : '')
                : 'Hover over a cable');
        };
        info.addTo(map);

        const professionalColorPalette = [
            '#2E86AB','#A23B72', '#F18F01',
            '#C73E1D','#6C757D', '#495057',
            '#4A90A4', '#8E44AD', '#D35400',
            '#27AE60', '#2C3E50', '#8B4513',
            '#556B2F', '#4682B4', '#CD853F',
            '#708090', '#2F4F4F', '#800080',
            '#B22222', '#228B22', '#4169E1',
            '#DC143C', '#FF8C00', '#9932CC',
            '#8FBC8F', '#483D8B', '#2E8B57',
            '#B8860B', '#A0522D', '#1E90FF',
            '#32CD32', '#FF6347','#4B0082',
            '#DAA520', '#008B8B', '#9400D3',
            '#FF4500', '#2E8B57', '#8B008B',
            '#556B2F'
        ];

        const specialCableColors = {
            'atlantic-crossing-1-ac-1': '#FF8C00',
            '2africa': '#000000',
            'africa-coast-to-europe-ace': '#DC143C',
            'west-africa-cable-system-wacs': '#4169E1',
            'maroc-telecom-west-africa': '#9932CC',
            'sat-3wasc': '#FF6347'
        };

        function getProfessionalCableColor(cableId, index) {
            if (specialCableColors.hasOwnProperty(cableId)) {
                return specialCableColors[cableId];
            }

            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }

            const colorIndex = (Math.abs(hash) + index) % professionalColorPalette.length;
            return professionalColorPalette[colorIndex];
        }


        // Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);
        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        function isAmericasCable(cableId) {
            return false;
        }
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;
                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    return lng >= -180 && lng <= -25;
                }
                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }
                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }

        // Define African countries
        const africaCountries = new Set([
            'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon',
            'Cape Verde', 'Central African Republic', 'Chad', 'Comoros', 'Congo', 'Congo, Dem. Rep.',
            'Congo, Rep.', 'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea',
            'Ethiopia', 'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho',
            'Liberia', 'Libya', 'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
            'Mozambique', 'Namibia', 'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal',
            'Seychelles', 'Sierra Leone', 'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Swaziland',
            'Tanzania', 'Togo', 'Tunisia', 'Uganda', 'Zambia', 'Zimbabwe'
        ]);
        // Define European countries
        const europeCountries = new Set([
            'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
            'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
            'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
            'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
            'Norway', 'Poland', 'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia',
            'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
        ]);
        function isAfricanCountry(country) {
            return africaCountries.has(country);
        }

        function isEuropeanCountry(country) {
            return europeCountries.has(country);
        }

        function isAfricaEuropeCountry(country) {
            return isAfricanCountry(country) || isEuropeanCountry(country);
        }

        function isAfricaEuropeConnection(country1, country2) {
            const isCountry1AfricaEurope = isAfricaEuropeCountry(country1);
            const isCountry2AfricaEurope = isAfricaEuropeCountry(country2);
            return isCountry1AfricaEurope && isCountry2AfricaEurope;
        }
        // Verify that a cable is visible on the filtered map
        function isCableVisibleOnMap(cableId) {
            return filteredCables.some(cable => cable.properties.id === cableId);
        }
        // Filter search results to only include cables visible on the map
        function filterSearchResultsToVisibleCables(searchResults) {
            if (Array.isArray(searchResults)) {
                // Direct array of cables
                const filtered = searchResults.filter(cable => {
                    const isVisible = isCableVisibleOnMap(cable.properties.id);
                    
                    return isVisible;
                });
                return filtered;
            } else if (searchResults && typeof searchResults === 'object') {
                // Object with direct and multiHop arrays
                const filtered = {
                    direct: searchResults.direct ? filterSearchResultsToVisibleCables(searchResults.direct) : [],
                    multiHop: searchResults.multiHop ? filterSearchResultsToVisibleCables(searchResults.multiHop) : []
                };
                return filtered;
            }
            return searchResults;
        }

        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {

                const criticalAfricanCables = new Set([
                    '2africa','west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe','sat-3wasc',
                    'equiano','africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1',  // Important Europe-Americas cable (NL-UK-DE-US)
                    // Cyprus cables - critical for Mediterranean routing
                    'turcyos-1', 'turcyos-2', 'cadmos', 'tamares-north', 'poseidon',
                    // European interconnection cables
                    'c-lion1', 'finland-estonia-connection-fec', 'finland-estonia-2-eesf-2', 'finland-estonia-3-eesf-3',
                    'sweden-finland-4-sfs-4', 'sweden-finland-link-sfl', 'sweden-estonia-ee-s-1', 'sweden-latvia',
                    'latvia-sweden-1-lv-se-1', 'denmark-sweden-15', 'denmark-sweden-16', 'denmark-sweden-17', 'denmark-sweden-18',
                    'denmark-poland-2', 'germany-denmark-3', 'nordbalt', 'baltic-sea-submarine-cable', 'baltica',
                    // Mediterranean and European cables
                    'italy-greece-1', 'italy-croatia', 'italy-albania', 'italy-malta', 'italy-monaco', 'italy-libya',
                    'adria-1', 'trans-adriatic-express', 'ionian', 'minoas-east-and-west', 'apollo-east-and-west',
                    'thetis', 'blue', 'medusa-submarine-cable-system', 'go-1-mediterranean-cable-system',
                    // Turkey and Eastern Mediterranean
                    'caucasus-cable-system', 'kafos', 'berytar', 'aletar', 'ugarit',
                    // UK and Ireland connections including Amitie
                    'celtic-norse', 'celtixconnect-1-cc-1', 'havhingstenceltixconnect-2-cc-2', 'crosschannel-fibre',
                    'pan-european-crossing-uk-ireland', 'pan-european-crossing-uk-belgium', 'rockabill', 'amitie',
                    'scotland-northern-ireland-1', 'scotland-northern-ireland-2', 'scotland-northern-ireland-3', 'scotland-northern-ireland-4',
                    // France connections
                    'groix-4', 'penbal-4', 'penbal-5', 'pencan-8', 'pencan-9', 'corse-continent-4-cc4', 'corse-continent-5-cc5',
                    // Iberian connections
                    'romulo', 'almera-melilla-alme', 'roquetas-melilla-cam', 'estepona-tetouan', 'trapani-kelibia',
                    // Other European critical cables
                    'europe-india-gateway-eig', 'seamewe-3', 'seamewe-4', 'seamewe-5', 'seamewe-6', 'imewe',
                    'flag-europe-asia-fea', 'te-northtgn-eurasiaseacomalexandrosmedex'
                ]);

                const filteredFeatures = data.features.filter(feature => {
                    if (criticalAfricanCables.has(feature.properties.id)) {
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas) {
                            return false;
                        }
                        if (isAsiaPacific) {
                            return false;
                        }
                        return true;
                    }
                    return true;
                });
                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };
                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredData, {
                    style: function(feature) {
                        const cableIndex = filteredData.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        const cableIndex = filteredData.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                // Only apply hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 4,
                                        opacity: 1
                                        // Removed color change - keep original color
                                    });
                                    info.update(feature.properties);
                                }
                            },
                            mouseout: function(e) {
                                // Only restore hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 2.5,
                                        opacity: 0.85
                                        // Removed color change - keep original color
                                    });
                                    info.update();
                                }
                            },
                            click: function(e) {

                                // Use professional individual cable selection
                                selectIndividualCable(feature.properties.id, feature);

                                // Prevent event bubbling to map click
                                L.DomEvent.stopPropagation(e);
                            }
                        });
                        // Create enhanced popup with professional styling
                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
                setTimeout(() => {
                    validateCableIdentification();
                }, 1000);
            });

        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {

                // Critical African landing points that should always be preserved
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti','lagos-nigeria',
                    'accra-ghana','dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);

                // Filter out landing points in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African landing points
                    if (criticalAfricanLandingPoints.has(feature.properties.id)) {
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isLandingPointInAmericas(feature.geometry.coordinates);
                        const isAsiaPacific = isLandingPointInAsiaPacific(feature.geometry.coordinates);

                        if (isAmericas) {
                            return false;
                        }
                        if (isAsiaPacific) {
                            return false;
                        }
                        return true;
                    }
                    return true;
                });

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control with improved tile options
        const baseMaps = {
            "CartoDB Positron": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "Esri World Street": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
                maxZoom: 19
            }),
            "CartoDB Voyager": L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19
            })
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        L.control.scale().addTo(map);

        let allCables = [];
        let filteredCables = []; // Cables visible on map after geographic filtering (synchronized with map display)
        let allLandingPoints = [];
        let availableCountries = new Set();
        let originalCableStyles = new Map();
        let isSearchActive = false;
        let currentCableIndex = 0;
        let cableCarouselActive = false;
        let currentCableNameLabel = null; // Store current cable name label
        let selectedCableId = null; // Track currently selected cable for individual highlighting
        let isIndividualCableSelected = false; // Track if individual cable selection is active

        // Multi-hop routing variables
        let countryGraph = new Map(); // Adjacency list for country connections
        let cableConnections = new Map(); // Maps country pairs to cables that connect them

        function selectIndividualCable(cableId, cableFeature) {
           
            selectedCableId = cableId;
            isIndividualCableSelected = true;
            // Store original styles for all cables (always refresh to handle dynamic loading)
            cableLayer.eachLayer(layer => {
                if (layer.feature && !originalCableStyles.has(layer.feature.properties.id)) {
                    const cableIndex = Array.from(cableLayer.getLayers()).indexOf(layer);
                    const originalColor = getProfessionalCableColor(layer.feature.properties.id, cableIndex);
                    originalCableStyles.set(layer.feature.properties.id, {
                        color: originalColor,
                        opacity: 0.85,
                        weight: 2.5
                    });
                }
            });

            let selectedLayer = null;

            let layersProcessed = 0;
            let selectedLayerFound = false;

            // Function to process individual feature layers (h
            function processLayer(layer, depth = 0) {
                layersProcessed++;
                const indent = '  '.repeat(depth);

                if (layer.feature && layer.feature.properties) {
                    // This is an individual feature layer
                    const layerCableId = layer.feature.properties.id;
                    const layerCableName = layer.feature.properties.name;

                    if (layerCableId === cableId) {
                        selectedLayer = layer;
                        selectedLayerFound = true;

                        // Get or calculate original style
                        let originalStyle = originalCableStyles.get(layerCableId);
                        if (!originalStyle) {
                            const originalColor = getProfessionalCableColor(layerCableId, layersProcessed);
                            originalStyle = {
                                color: originalColor,
                                opacity: 0.85,
                                weight: 2.5
                            };
                            originalCableStyles.set(layerCableId, originalStyle);
                        }

                        layer.setStyle({
                            color: originalStyle.color, 
                            opacity: 1.0, 
                            weight: 4.5, 
                            dashArray: null
                        });
                        // Bring to front for better visibility
                        layer.bringToFront();

                    } else {
                        // Fade all other cables - reduce opacity and lighten color
                        layer.setStyle({
                            color: '#bbb', 
                            opacity: 0.25, 
                            weight: 1.5, 
                            dashArray: null
                        });
                    }
                } else if (layer.eachLayer) {
                    // This is a layer group, process its children
                    layer.eachLayer(childLayer => processLayer(childLayer, depth + 1));
                } 
            }
            // Process all layers (including nested ones)
            cableLayer.eachLayer(layer => processLayer(layer));

            if (selectedLayer) {
                try {
                    const bounds = selectedLayer.getBounds();
                    if (bounds && bounds.isValid()) {
                        map.fitBounds(bounds, {
                            padding: [60, 60], 
                            maxZoom: 6, 
                            animate: true,
                            duration: 1.2 
                        });
                        // Show popup after zoom completes
                        setTimeout(() => {
                            if (selectedLayer.getPopup()) {
                                selectedLayer.openPopup();
                            }
                        }, 1300);
                    }
                } catch (error) {
                    if (cableFeature && cableFeature.geometry) {
                        zoomToCableGeometry(cableFeature);
                    }
                }
            }
        }

        function clearIndividualCableSelection() {

            if (!isIndividualCableSelected) {
                return; // Exit if no individual cable is selected
            }

            selectedCableId = null;
            isIndividualCableSelected = false;

            // Function to restore styles for individual feature layers
            function restoreLayer(layer, layerIndex = 0) {
                if (layer.feature && layer.feature.properties) {
                    const cableId = layer.feature.properties.id;
                    const originalColor = getProfessionalCableColor(cableId, layerIndex);

                    // Restore to normal style
                    layer.setStyle({
                        color: originalColor,
                        opacity: 0.85,
                        weight: 2.5,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                } else if (layer.eachLayer) {
                    // This is a layer group, process its children
                    layer.eachLayer(childLayer => restoreLayer(childLayer, layerIndex));
                }
            }
            // Restore all cables to their normal appearance (including nested ones)
            let layerIndex = 0;
            cableLayer.eachLayer(layer => {
                restoreLayer(layer, layerIndex);
                layerIndex++;
            });
        }

        function zoomToCableGeometry(cableFeature) {
            if (!cableFeature.geometry || !cableFeature.geometry.coordinates) {
                return;
            }
            const coords = cableFeature.geometry.coordinates;
            let bounds = L.latLngBounds();

            function addCoordinatesToBounds(coordinates) {
                if (Array.isArray(coordinates[0])) {
                    if (Array.isArray(coordinates[0][0])) {
                        // MultiLineString
                        coordinates.forEach(lineString => {
                            lineString.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        });
                    } else {
                        // LineString
                        coordinates.forEach(coord => {
                            bounds.extend([coord[1], coord[0]]);
                        });
                    }
                }
            }
            addCoordinatesToBounds(coords);

            if (bounds.isValid()) {
                map.fitBounds(bounds, {
                    padding: [60, 60],
                    maxZoom: 6,
                    animate: true,
                    duration: 1.2
                });
            }
        }
        // Add map click handler to clear search if active and close persistent popups
        map.on('click', function(e) {
            if (isSearchActive) {
                clearSearch();
            } else if (isIndividualCableSelected) {
                // Clear individual cable selection and restore normal view
                clearIndividualCableSelection();
            } else {
                // Clear any persistent cable name labels when clicking on empty map area
                if (typeof clearCableNameLabel === 'function') {
                    clearCableNameLabel();
                }
            }
        });

        // Load cable and landing point data for sidebar
        Promise.all([
            fetch('../cable/cable-geo.json').then(response => response.json()),
            fetch('../landing-point/landing-point-geo.json').then(response => response.json())
        ]).then(([cableData, landingPointData]) => {
            allCables = cableData.features;
            allLandingPoints = landingPointData.features;

            // Initialize filteredCables if not already set by map loading
            if (filteredCables.length === 0) {
                filteredCables = allCables;
            }

            extractAvailableCountries();
            setupSearchFunctionality();
            setupHighlightingFunctionality();

            // Initialize country graph for multi-hop routing
            buildCountryGraph().then(() => {

            }).catch(error => {
            });
        });

        function extractAvailableCountries() {
            const africaCountries = new Set([
                'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon',
                'Cape Verde', 'Central African Republic', 'Chad', 'Comoros', 'Congo', 'Congo, Dem. Rep.',
                'Congo, Rep.', 'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea',
                'Ethiopia', 'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho',
                'Liberia', 'Libya', 'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
                'Mozambique', 'Namibia', 'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal',
                'Seychelles', 'Sierra Leone', 'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Swaziland',
                'Tanzania', 'Togo', 'Tunisia', 'Uganda', 'Zambia', 'Zimbabwe'
            ]);

            const europeCountries = new Set([
                'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
                'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
                'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
                'Norway', 'Poland', 'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia',
                'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
            ]);

            // Extract countries from landing points that are in Africa or Europe
            allLandingPoints.forEach(point => {
                if (point.properties && point.properties.name) {
                    const nameParts = point.properties.name.split(',');
                    if (nameParts.length >= 2) {
                        const country = nameParts[nameParts.length - 1].trim();
                        if (africaCountries.has(country) || europeCountries.has(country)) {
                            availableCountries.add(country);
                        }
                    }
                }
            });

            // Add some known countries that might be in the cable data but not easily extracted
            const knownAfricanEuropeanCountries = [
                'South Africa', 'Egypt', 'Morocco', 'Nigeria', 'Kenya', 'Ghana', 'Senegal', 'Tanzania',
                'Angola', 'Mozambique', 'Madagascar', 'Mauritius', 'Seychelles', 'Djibouti', 'Somalia',
                'United Kingdom', 'France', 'Spain', 'Italy', 'Germany', 'Netherlands', 'Portugal',
                'Greece', 'Norway', 'Denmark', 'Sweden', 'Finland', 'Ireland', 'Belgium', 'Malta',
                'Cyprus', 'Bulgaria', 'Romania', 'Croatia', 'Albania', 'Turkey', 'Russia'
            ];

            knownAfricanEuropeanCountries.forEach(country => {
                if (africaCountries.has(country) || europeCountries.has(country)) {
                    availableCountries.add(country);
                }
            });
        }

        function setupSearchFunctionality() {
            const fromCountryInput = document.getElementById('fromCountry');
            const toCountryInput = document.getElementById('toCountry');
            const fromDropdown = document.getElementById('fromCountryDropdown');
            const toDropdown = document.getElementById('toCountryDropdown');
            const searchBtn = document.getElementById('searchBtn');
            const clearBtn = document.getElementById('clearBtn');

            const countriesArray = Array.from(availableCountries).sort();

            setupAutocomplete(fromCountryInput, fromDropdown, countriesArray);
            setupAutocomplete(toCountryInput, toDropdown, countriesArray);

            // Enable search button when both countries are selected
            function updateSearchButton() {
                const fromValid = availableCountries.has(fromCountryInput.value);
                const toValid = availableCountries.has(toCountryInput.value);
                searchBtn.disabled = !(fromValid && toValid && fromCountryInput.value !== toCountryInput.value);

                // Auto-clear search when both fields are empty
                if (fromCountryInput.value === '' && toCountryInput.value === '' && isSearchActive) {

                    // Brief visual feedback that search is being cleared
                    const resultsDiv = document.getElementById('cableResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML = '<div class="no-results">🔄 Restoring original map view...</div>';
                        setTimeout(() => {
                            clearSearchResults();
                        }, 300); // Small delay for visual feedback
                    } else {
                        clearSearchResults();
                    }
                }
            }

            fromCountryInput.addEventListener('input', updateSearchButton);
            toCountryInput.addEventListener('input', updateSearchButton);

            // Additional event listeners to catch all ways of clearing input
            fromCountryInput.addEventListener('keyup', updateSearchButton);
            toCountryInput.addEventListener('keyup', updateSearchButton);
            fromCountryInput.addEventListener('change', updateSearchButton);
            toCountryInput.addEventListener('change', updateSearchButton);

            // Search functionality
            searchBtn.addEventListener('click', performSearch);
            clearBtn.addEventListener('click', clearSearch);
        }

        function setupAutocomplete(input, dropdown, countries) {
            input.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                dropdown.innerHTML = '';

                if (value.length === 0) {
                    dropdown.style.display = 'none';
                    return;
                }

                const filtered = countries.filter(country =>
                    country.toLowerCase().includes(value)
                ).slice(0, 10); // Limit to 10 results

                if (filtered.length > 0) {
                    filtered.forEach(country => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.textContent = country;
                        item.addEventListener('click', function() {
                            input.value = country;
                            dropdown.style.display = 'none';
                            input.dispatchEvent(new Event('input'));
                        });
                        dropdown.appendChild(item);
                    });
                    dropdown.style.display = 'block';
                } else {
                    dropdown.style.display = 'none';
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        function setupHighlightingFunctionality() {
            const highlightConnectionPointsCheckbox = document.getElementById('highlightConnectionPoints');
            const highlightCountryInput = document.getElementById('highlightCountry');
            const highlightCountryDropdown = document.getElementById('highlightCountryDropdown');

            const countriesArray = Array.from(availableCountries).sort();

            // Setup autocomplete for country highlighting
            setupAutocomplete(highlightCountryInput, highlightCountryDropdown, countriesArray);

            // Handle connection point highlighting checkbox
            highlightConnectionPointsCheckbox.addEventListener('change', function() {
                landingPointHighlightSettings.highlightConnectionPoints = this.checked;
                refreshCableDisplays();
            });

            // Handle country highlighting input
            highlightCountryInput.addEventListener('input', function() {
                const selectedCountry = this.value.trim();
                if (selectedCountry === '') {
                    landingPointHighlightSettings.highlightCountry = null;
                } else if (availableCountries.has(selectedCountry)) {
                    landingPointHighlightSettings.highlightCountry = selectedCountry;
                } else {
                    // Don't update if it's not a valid country (user is still typing)
                    return;
                }
                refreshCableDisplays();
            });

            // Clear country highlighting when input is cleared
            highlightCountryInput.addEventListener('keyup', function(e) {
                if (e.key === 'Escape' || (e.key === 'Backspace' && this.value === '')) {
                    this.value = '';
                    landingPointHighlightSettings.highlightCountry = null;
                    refreshCableDisplays();
                }
            });
        }

        // Function to refresh all cable displays with current highlighting settings
        function refreshCableDisplays() {
            // Refresh search results if search is active
            if (isSearchActive) {
                const cableResults = document.getElementById('cableResults');
                if (cableResults && cableResults.innerHTML) {
                    // Re-trigger the current search to refresh displays
                    const fromCountry = document.getElementById('fromCountry').value;
                    const toCountry = document.getElementById('toCountry').value;
                    if (fromCountry && toCountry) {
                        performSearch();
                    }
                }
            }
        }

        function getCableStyle(feature, cableIndex) {
            const cableColor = getProfessionalCableColor(feature.properties.id, cableIndex);
            const priority = feature.properties.priority || 'main';
            if (priority === 'main') {
                return {
                    color: cableColor,
                    weight: 3.5,
                    opacity: 0.85,
                    dashArray: null
                };
            } else {
                // Secondary/alternative: orange dashed
                return {
                    color: '#F18F01',
                    weight: 2.5,
                    opacity: 0.85,
                    dashArray: '6, 6'
                };
            }
        }

        let allCableGeoJSON = null;
        let currentCableGeoJSON = null;

        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                allCableGeoJSON = data;
            });

        // Helper to clear and add only matching cables
        function showOnlyCables(connectingCables) {
            cableLayer.clearLayers();
            if (!connectingCables || connectingCables.length === 0) return;
            // Build a GeoJSON with only the matching features
            const features = connectingCables.map(cable => cable);
            const filteredGeoJSON = {
                ...allCableGeoJSON,
                features: features.map(c => c.feature || c)
            };
            currentCableGeoJSON = filteredGeoJSON;
            L.geoJSON(filteredGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            // Only restore hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // Helper to restore all cables
        function restoreAllCables() {
            cableLayer.clearLayers();
            if (!allCableGeoJSON) return;
            L.geoJSON(allCableGeoJSON, {
                style: function(feature) {
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    // (reuse your onEachFeature code from above)
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }
        function clearSearchResults() {

            // Hide dropdowns
            document.getElementById('fromCountryDropdown').style.display = 'none';
            document.getElementById('toCountryDropdown').style.display = 'none';

            // Hide results
            document.getElementById('resultsHeader').style.display = 'none';
            document.getElementById('cableResults').innerHTML = '';

            // Clear cable name label
            clearCableNameLabel();

            restoreAfricaEuropeCables();
            resetLandingPointHighlights();

            isSearchActive = false;
        }

        function clearSearch() {
            document.getElementById('fromCountry').value = '';
            document.getElementById('toCountry').value = '';
            document.getElementById('searchBtn').disabled = true;

            // Clear search context for highlighting
            landingPointHighlightSettings.searchFromCountry = null;
            landingPointHighlightSettings.searchToCountry = null;

            clearSearchResults();
        }

        async function findConnectingCables(fromCountry, toCountry) {
            const connectingCables = [];

            document.getElementById('cableResults').innerHTML = '<div class="no-results">Searching cables...</div>';

            // We need to load individual cable files to get landing point information
            // Use filtered cables to match what's visible on the map
            const promises = filteredCables.map(async (cable) => {
                try {
                    const cableResponse = await fetch(`../cable/${cable.properties.id}.json`);
                    if (!cableResponse.ok) {
                        throw new Error(`HTTP ${cableResponse.status}`);
                    }
                    const cableData = await cableResponse.json();

                    if (cableData.landing_points && Array.isArray(cableData.landing_points)) {
                        const countries = cableData.landing_points.map(lp => lp.country).filter(Boolean);

                        if (countries.includes(fromCountry) && countries.includes(toCountry)) {
                            return {
                                ...cable,
                                cableData: cableData
                            };
                        }
                    }
                } catch (error) {
                    console.warn(`Could not load cable data for ${cable.properties.id}:`, error);
                }
                return null;
            });

            // Wait for all promises to resolve and filter out null results
            const results = await Promise.all(promises);
            const validResults = results.filter(result => result !== null);

            return validResults;
        }
        function updateMapVisualization(connectingCables) {

            // Store original styles if not already stored
            if (originalCableStyles.size === 0) {
                cableLayer.eachLayer(layer => {
                    if (layer.feature) {
                        originalCableStyles.set(layer.feature.properties.id, {
                            color: layer.options.color,
                            opacity: layer.options.opacity,
                            weight: layer.options.weight
                        });
                    }
                });
            }

            const connectingCableIds = new Set(connectingCables.map(cable => cable.properties.id));

            // Update all cable layers with extreme visibility contrast 
            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    const cableId = layer.feature.properties.id;

                    if (connectingCableIds.has(cableId)) {
                        const originalStyle = originalCableStyles.get(cableId);
                        layer.setStyle({
                            color: originalStyle.color, 
                            opacity: 1.0, 
                            weight: 4, 
                            dashArray: null 
                        });

                        layer.bringToFront();

                    } else {
                        // BARELY VISIBLE: Make all other cables almost invisible 
                        layer.setStyle({
                            color: '#cccccc', 
                            opacity: 0.1, 
                            weight: 1, 
                            dashArray: null
                        });
                    }
                }
            });

            // Count total cables for verification
            let totalCables = 0;
            let highlightedCount = 0;
            let dimmedCount = 0;

            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    totalCables++;
                    const cableId = layer.feature.properties.id;
                    if (connectingCableIds.has(cableId)) {
                        highlightedCount++;
                    } else {
                        dimmedCount++;
                    }
                }
            }); 
        }
        function resetMapVisualization() {

            cableLayer.eachLayer(layer => {
                if (layer.feature && originalCableStyles.has(layer.feature.properties.id)) {
                    const originalStyle = originalCableStyles.get(layer.feature.properties.id);

                    // Apply original style smoothly
                    layer.setStyle({
                        color: originalStyle.color,
                        opacity: originalStyle.opacity,
                        weight: originalStyle.weight,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                }
            });
        }

        // Global variable to store current highlighting preferences
        let landingPointHighlightSettings = {
            highlightConnectionPoints: false,
            highlightCountry: null,
            searchFromCountry: null,
            searchToCountry: null
        };

        // Helper function to determine if a landing point is relevant for the connection between two countries
        function isConnectionRelevantLandingPoint(landingPoints, index, fromCountry, toCountry) {
            if (!landingPoints || !fromCountry || !toCountry) return false;

            const landingPoint = landingPoints[index];
            if (!landingPoint) return false;

            // A landing point is connection-relevant if:
            // 1. It belongs to one of the searched countries, OR
            // 2. It's a necessary intermediate point between the two countries in the cable route

            // Check if it's one of the searched countries
            if (landingPoint.country === fromCountry || landingPoint.country === toCountry) {
                return true;
            }

            // For intermediate points, check if they're between the searched countries in the route
            const fromIndex = landingPoints.findIndex(lp => lp.country === fromCountry);
            const toIndex = landingPoints.findIndex(lp => lp.country === toCountry);

            if (fromIndex === -1 || toIndex === -1) return false;

            const minIndex = Math.min(fromIndex, toIndex);
            const maxIndex = Math.max(fromIndex, toIndex);

            // Include landing points that are between the two searched countries
            return index >= minIndex && index <= maxIndex;
        }

        // Helper function to format landing points for display with highlighting options
        function formatLandingPoints(landingPoints, options = {}) {
            if (!landingPoints || !Array.isArray(landingPoints) || landingPoints.length === 0) {
                return '<div class="cable-landing-points"><div class="landing-points-label">Landing Points:</div><div style="font-style: italic; color: #95a5a6;">Not available</div></div>';
            }

            // Merge with global settings
            const settings = {
                highlightConnectionPoints: landingPointHighlightSettings.highlightConnectionPoints,
                highlightCountry: landingPointHighlightSettings.highlightCountry,
                searchFromCountry: landingPointHighlightSettings.searchFromCountry,
                searchToCountry: landingPointHighlightSettings.searchToCountry,
                ...options
            };

            // Show detailed landing points with city and country information
            const landingPointsHtml = landingPoints.map((lp, index) => {
                const locationName = lp.name || `${lp.country || 'Unknown'}`;
                const arrow = index < landingPoints.length - 1 ? '<span class="route-arrow">➜</span>' : '';

                // Determine highlighting classes
                let cssClasses = 'landing-point';
                let title = locationName;

                // Check if this is a connection-relevant landing point
                const isConnectionRelevant = isConnectionRelevantLandingPoint(
                    landingPoints, index, settings.searchFromCountry, settings.searchToCountry
                );
                if (settings.highlightConnectionPoints && isConnectionRelevant) {
                    cssClasses += ' landing-point-connection';
                    title += ' (Connection Point)';
                }

                // Check if this matches the highlighted country
                if (settings.highlightCountry && lp.country === settings.highlightCountry) {
                    cssClasses += ' landing-point-country-highlight';
                    title += ` (Landing point of ${settings.highlightCountry})`;
                }

                return `<span class="${cssClasses}" title="${title}">${locationName}</span>${arrow}`;
            }).join('');

            // If there are too many landing points, show a summary
            if (landingPoints.length > 8) {
                const countries = [...new Set(landingPoints.map(lp => lp.country).filter(Boolean))];
                const routeHtml = countries.map((country, index) => {
                    const arrow = index < countries.length - 1 ? '<span class="route-arrow">➜</span>' : '';

                    // Determine highlighting for country summary
                    let cssClasses = 'landing-point';
                    let title = country;

                    // Check if this is a connection-relevant country
                    const isConnectionRelevant = (settings.searchFromCountry && settings.searchToCountry) &&
                        (country === settings.searchFromCountry || country === settings.searchToCountry ||
                         (countries.indexOf(settings.searchFromCountry) !== -1 &&
                          countries.indexOf(settings.searchToCountry) !== -1 &&
                          index >= Math.min(countries.indexOf(settings.searchFromCountry), countries.indexOf(settings.searchToCountry)) &&
                          index <= Math.max(countries.indexOf(settings.searchFromCountry), countries.indexOf(settings.searchToCountry))));

                    if (settings.highlightConnectionPoints && isConnectionRelevant) {
                        cssClasses += ' landing-point-connection';
                        title += ' (Connection Point)';
                    }

                    // Check if this matches the highlighted country
                    if (settings.highlightCountry && country === settings.highlightCountry) {
                        cssClasses += ' landing-point-country-highlight';
                        title += ` (Landing point of ${settings.highlightCountry})`;
                    }

                    return `<span class="${cssClasses}" title="${title}">${country}</span>${arrow}`;
                }).join('');

                return `
                    <div class="cable-landing-points">
                        <div class="landing-points-label">Landing Points (${landingPoints.length} locations):</div>
                        <div class="landing-points-route">${routeHtml}</div>
                    </div>
                `;
            }

            return `
                <div class="cable-landing-points">
                    <div class="landing-points-label">Landing Points:</div>
                    <div class="landing-points-route">${landingPointsHtml}</div>
                </div>
            `;
        }

        function displaySearchResults(connectingCables, fromCountry, toCountry) {
            const resultsHeader = document.getElementById('resultsHeader');
            const cableResults = document.getElementById('cableResults');

            if (connectingCables.length === 0) {
                resultsHeader.style.display = 'block';
                resultsHeader.textContent = 'No cables found';
                cableResults.innerHTML = '<div class="no-results">No submarine cables found connecting these countries.</div>';
                return;
            }

            resultsHeader.style.display = 'block';
            resultsHeader.textContent = `Found ${connectingCables.length} cable${connectingCables.length > 1 ? 's' : ''}:`;

            cableResults.innerHTML = '';

            connectingCables.forEach(cable => {
                const cableDiv = document.createElement('div');
                cableDiv.className = 'cable-result';

                const actualCableColor = getActualCableColor(cable.properties.id);
                const cableData = cable.cableData;

                cableDiv.innerHTML = `
                    <div class="cable-name">
                        <span class="cable-color-indicator" style="background-color: ${actualCableColor}"></span>
                        ${cable.properties.name}
                    </div>
                    <div class="cable-details">
                        ${cableData.length ? `Length: ${cableData.length}` : ''}
                        ${cableData.rfs ? ` • RFS: ${cableData.rfs}` : ''}
                        ${cableData.owners ? `<br>Owners: ${cableData.owners}` : ''}
                    </div>
                    ${formatLandingPoints(cableData.landing_points)}
                `;

                // Add enhanced click handler with visual feedback
                cableDiv.addEventListener('click', () => {
                    // Add visual feedback to the clicked result
                    addClickFeedback(cableDiv);
                    centerMapOnCable(cable);
                    selectIndividualCable(cable.properties.id, cable);
                });
                cableResults.appendChild(cableDiv);
            });
        }

        function getActualCableColor(cableId) {
            let actualColor = '#3498db';

            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    if (originalCableStyles.has(cableId)) {
                        actualColor = originalCableStyles.get(cableId).color;
                    } else if (layer.options.color) {
                        actualColor = layer.options.color;
                    } else if (layer.feature.properties.color) {
                        actualColor = layer.feature.properties.color;
                    }

                    if (!actualColor || actualColor === 'undefined') {
                        actualColor = '#3498db';
                    }
                }
            });

            return actualColor;
        }

        function validateCableIdentification() {
            let identifiedCables = 0;
            let totalCables = 0;

            cableLayer.eachLayer(layer => {
                totalCables++;
                if (layer.feature && layer.feature.properties.id) {
                    identifiedCables++;
                } else {
                    console.warn('Cable layer without proper identification:', layer);
                }
            });

            return identifiedCables === totalCables;
        }

        function addClickFeedback(element) {
            // Add a brief visual feedback when cable result is clicked
            element.style.transform = 'scale(0.98)';
            element.style.backgroundColor = '#e8f4fd';
            element.style.borderColor = '#3498db';

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.backgroundColor = 'white';
                element.style.borderColor = '#e8e8e8';
            }, 200);
        }

        function centerMapOnCable(cable) {
            if (cable.geometry && cable.geometry.coordinates) {
                // Calculate bounds of the cable
                const coords = cable.geometry.coordinates;
                let bounds = L.latLngBounds();

                function addCoordinatesToBounds(coordinates) {
                    if (Array.isArray(coordinates[0])) {
                        if (Array.isArray(coordinates[0][0])) {
                            // MultiLineString
                            coordinates.forEach(lineString => {
                                lineString.forEach(coord => {
                                    bounds.extend([coord[1], coord[0]]);
                                });
                            });
                        } else {
                            // LineString
                            coordinates.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        }
                    }
                }
                 addCoordinatesToBounds(coords);

                if (bounds.isValid()) {
                    // Enhanced map centering with better padding and zoom control
                    map.fitBounds(bounds, {
                        padding: [30, 30],
                        maxZoom: 6
                    });
                }
            }
        }

        function showCableNameLabel(cable) {
            clearCableNameLabel();
            if (!cable || !cable.properties || !cable.properties.name) {
                return;
            }

            // Find the cable layer to get its geometry
            let cableLayer = null;

            // Search through all cable layers to find the matching one
            map.eachLayer(layer => {
                if (layer.feature && layer.feature.properties &&
                    layer.feature.properties.id === cable.properties.id) {
                    cableLayer = layer;
                }
            });

            if (!cableLayer) {
                return;
            }

            // Get a point on the cable line for label placement
            let labelPosition = null;

            if (cableLayer.feature.geometry.type === 'LineString') {
                // Get coordinates from the cable geometry
                const coordinates = cableLayer.feature.geometry.coordinates;
                if (coordinates && coordinates.length > 0) {
                    // Use the middle point of the cable line
                    const middleIndex = Math.floor(coordinates.length / 2);
                    const coord = coordinates[middleIndex];
                    labelPosition = L.latLng(coord[1], coord[0]); // Note: GeoJSON is [lng, lat]
                }
            } else if (cableLayer.feature.geometry.type === 'MultiLineString') {
                // For MultiLineString, use the first line's middle point
                const firstLine = cableLayer.feature.geometry.coordinates[0];
                if (firstLine && firstLine.length > 0) {
                    const middleIndex = Math.floor(firstLine.length / 2);
                    const coord = firstLine[middleIndex];
                    labelPosition = L.latLng(coord[1], coord[0]);
                }
            }

            // Fallback to bounds center if geometry method fails
            if (!labelPosition) {
                const bounds = cableLayer.getBounds();
                if (bounds && bounds.isValid()) {
                    labelPosition = bounds.getCenter();
                } else {
                    console.log('Could not determine label position for cable');
                    return;
                }
            }

            // Create popup content with same styling as  cable popups
            const popupContent = `
                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <h4 style="margin: 0; color: #2c3e50; font-size: 14px;">${cable.properties.name}</h4>
                </div>
            `;

            // Create and show the popup directly on the cable line
            currentCableNameLabel = L.popup({
                closeButton: true,
                autoClose: false,
                closeOnClick: false,
                className: 'cable-name-popup'
            })
            .setLatLng(labelPosition)
            .setContent(popupContent)
            .openOn(map);

            // Note: Removed auto-close timeout to keep tooltip persistent until user action
        }

        function clearCableNameLabel() {
            if (currentCableNameLabel) {
                map.closePopup(currentCableNameLabel);
                currentCableNameLabel = null;
            }
        }

        function highlightSpecificCable(cableId) {

            // Find the cable data for the name label
            let selectedCable = null;
            allCables.forEach(cable => {
                if (cable.properties && cable.properties.id === cableId) {
                    selectedCable = cable;
                }
            });

            if (selectedCable) {
                showCableNameLabel(selectedCable);
            }

            // Find and highlight the specific cable with enhanced visual effects
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    const originalStyle = originalCableStyles.get(cableId);

                    // Create a pulsing highlight effect
                    const pulseHighlight = () => {
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 8, 
                            dashArray: null 
                        });

                        // Add a subtle glow effect by creating a temporary shadow layer
                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 0.9,
                                weight: 7
                            });
                        }, 300);

                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 8
                            });
                        }, 600);
                    };

                    // Start the pulse effect
                    pulseHighlight();
                    // Show popup with cable info if available
                    if (layer.getPopup()) {
                        // Find a good position for the popup (center of cable bounds)
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                        } else {
                            layer.openPopup();
                        }
                    }

                    // Reset to search result state after 4 seconds (but keep popup open)
                    setTimeout(() => {
                        if (isSearchActive) {
                            // Return to search result highlighting 
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 3.5 
                            });
                        } else {
                            // If search was cleared, return to original state
                            layer.setStyle(originalStyle);
                        }

                    }, 4000);
                    layer.bringToFront();
                }
            });
        }

        let highlightedLandingPointIds = new Set();
        function highlightLandingPointsForCables(connectingCables) {
            highlightedLandingPointIds.clear();
            connectingCables.forEach(cable => {
                if (cable.cableData && cable.cableData.landing_points) {
                    cable.cableData.landing_points.forEach(lp => {
                        if (lp.id) highlightedLandingPointIds.add(lp.id);
                    });
                }
            });
            landingPointLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.id) {
                    if (highlightedLandingPointIds.has(layer.feature.properties.id)) {
                        layer.setStyle({ fillColor: '#FFD700', radius: 10 }); // Gold and larger
                        layer.bringToFront();
                    } else {
                        layer.setStyle({ fillColor: '#FF0000', radius: 5 }); // Default
                    }
                }
            });
        }
        function resetLandingPointHighlights() {
            highlightedLandingPointIds.clear();
            landingPointLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.id) {
                    layer.setStyle({ fillColor: '#FF0000', radius: 5 });
                }
            });
        }
       
        let filteredAfricaEuropeGeoJSON = null;

        // When you first filter and display Africa/Europe cables
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                const criticalAfricanCables = new Set([
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe',
                    'sat-3wasc',
                    'equiano',
                    'africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1',  // Important Europe-Americas cable (NL-UK-DE-US)
                    // Cyprus cables - critical for Mediterranean routing
                    'turcyos-1', 'turcyos-2', 'cadmos', 'tamares-north', 'poseidon',
                    // European interconnection cables
                    'c-lion1', 'finland-estonia-connection-fec', 'finland-estonia-2-eesf-2', 'finland-estonia-3-eesf-3',
                    'sweden-finland-4-sfs-4', 'sweden-finland-link-sfl', 'sweden-estonia-ee-s-1', 'sweden-latvia',
                    'latvia-sweden-1-lv-se-1', 'denmark-sweden-15', 'denmark-sweden-16', 'denmark-sweden-17', 'denmark-sweden-18',
                    'denmark-poland-2', 'germany-denmark-3', 'nordbalt', 'baltic-sea-submarine-cable', 'baltica',
                    // Mediterranean and European cables
                    'italy-greece-1', 'italy-croatia', 'italy-albania', 'italy-malta', 'italy-monaco', 'italy-libya',
                    'adria-1', 'trans-adriatic-express', 'ionian', 'minoas-east-and-west', 'apollo-east-and-west',
                    'thetis', 'blue', 'medusa-submarine-cable-system', 'go-1-mediterranean-cable-system',
                    // Turkey and Eastern Mediterranean
                    'caucasus-cable-system', 'kafos', 'berytar', 'aletar', 'ugarit',
                    // UK and Ireland connections including Amitie
                    'celtic-norse', 'celtixconnect-1-cc-1', 'havhingstenceltixconnect-2-cc-2', 'crosschannel-fibre',
                    'pan-european-crossing-uk-ireland', 'pan-european-crossing-uk-belgium', 'rockabill', 'amitie',
                    'scotland-northern-ireland-1', 'scotland-northern-ireland-2', 'scotland-northern-ireland-3', 'scotland-northern-ireland-4',
                    // France connections
                    'groix-4', 'penbal-4', 'penbal-5', 'pencan-8', 'pencan-9', 'corse-continent-4-cc4', 'corse-continent-5-cc5',
                    // Iberian connections
                    'romulo', 'almera-melilla-alme', 'roquetas-melilla-cam', 'estepona-tetouan', 'trapani-kelibia',
                    // Other European critical cables
                    'europe-india-gateway-eig', 'seamewe-3', 'seamewe-4', 'seamewe-5', 'seamewe-6', 'imewe',
                    'flag-europe-asia-fea', 'te-northtgn-eurasiaseacomalexandrosmedex'
                ]);
                // Filter out Americas and Asia-Pacific cables, keep only Africa/Europe and critical cables
                const filteredFeatures = data.features.filter(feature => {
                    if (criticalAfricanCables.has(feature.properties.id)) return true;
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);
                        if (isAmericas || isAsiaPacific) return false;
                        return true;
                    }
                    return true;
                });
                filteredAfricaEuropeGeoJSON = {
                    ...data,
                    features: filteredFeatures
                };

                filteredCables = filteredFeatures;
              
                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredAfricaEuropeGeoJSON, {
                    style: function(feature) {
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                // Only apply hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 4,
                                        opacity: 1
                                    });
                                    info.update(feature.properties);
                                }
                            },
                            mouseout: function(e) {
                                // Only restore hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 2.5,
                                        opacity: 0.85
                                    });
                                    info.update();
                                }
                            },
                            click: function(e) {
                                selectIndividualCable(feature.properties.id, feature);
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to restore the Africa/Europe filtered cables
        function restoreAfricaEuropeCables() {
            cableLayer.clearLayers();
            if (!filteredAfricaEuropeGeoJSON) return;
            L.geoJSON(filteredAfricaEuropeGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            // Only restore hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // Enhanced search functionality
        async function performSearch() {
            const fromCountry = document.getElementById('fromCountry').value;
            const toCountry = document.getElementById('toCountry').value;
            const searchType = document.getElementById('searchType').value;

            // Update highlighting context for connection-relevant points
            landingPointHighlightSettings.searchFromCountry = fromCountry;
            landingPointHighlightSettings.searchToCountry = toCountry;

            // Validate that both countries are within Europe/Africa scope
            if (!isEuropeanOrAfricanCountry(fromCountry) || !isEuropeanOrAfricanCountry(toCountry)) {
                const resultsContainer = document.getElementById('cableResults');
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">🌍</div>
                        <div class="no-results-text">Search limited to European and African countries</div>
                        <div class="no-results-suggestion">This map focuses on Europe-Africa submarine cable connections. Please select countries from these regions.</div>
                    </div>
                `;
                return;
            }

            // Show loading state
            const searchBtn = document.getElementById('searchBtn');
            const originalText = searchBtn.textContent;
            searchBtn.textContent = 'Searching...';
            searchBtn.disabled = true;

            try {
                // Find cables connecting these countries based on search type
                const rawSearchResults = await findConnectingCables(fromCountry, toCountry, searchType);

                // Filter search results to only include cables visible on the map
                const searchResults = filterSearchResultsToVisibleCables(rawSearchResults);

                let allCables = [];
                if (searchType === 'both') {
                    // Combine direct and multi-hop results
                    allCables = [...(searchResults.direct || []), ...(searchResults.multiHop || [])];
                } else {
                    allCables = searchResults || [];
                }
                // Verify all search results are visible on the map
                const invisibleCables = allCables.filter(cable => !isCableVisibleOnMap(cable.properties.id));
                if (invisibleCables.length > 0) {
                       invisibleCables.map(c => c.properties.name);
                } 

                showOnlyCables(allCables);
                highlightLandingPointsForCables(allCables);

                // Display results in sidebar with enhanced multi-hop support
                if (searchType === 'both') {
                    displayEnhancedSearchResults(searchResults, fromCountry, toCountry);
                } else {
                    displaySearchResults(allCables, fromCountry, toCountry, searchType);
                }

                isSearchActive = true;

            } catch (error) {
                document.getElementById('cableResults').innerHTML = '<div class="no-results">Search failed. Please try again.</div>';
            } finally {
                // Restore search button
                searchBtn.textContent = originalText;
                searchBtn.disabled = false;
            }
        }

        // Enhanced display functions
        function displaySearchResults(cables, fromCountry, toCountry, searchType = 'direct') {
            const resultsContainer = document.getElementById('cableResults');

            if (cables.length === 0) {
                const searchTypeText = searchType === 'multi-hop' ? 'multi-hop routes' : 'direct connections';
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">➤</div>
                        <div class="no-results-text">No ${searchTypeText} found between ${fromCountry} and ${toCountry}</div>
                        <div class="no-results-suggestion">Try searching for different countries or a different search type.</div>
                    </div>
                `;
                return;
            }

            // Group cables by path type for multi-hop results
            const directCables = cables.filter(c => c.pathType === 'direct' || !c.pathType);
            const multiHopCables = cables.filter(c => c.pathType === 'multi-hop');

            let html = `
                <div class="search-results-header">
                    <div class="results-title">
                        <span class="results-icon">🔗</span>
                        Found ${cables.length} connection${cables.length !== 1 ? 's' : ''}
                    </div>
                    <div class="results-route">
                        <span class="country-tag">${fromCountry}</span>
                        <span class="route-arrow">➜</span>
                        <span class="country-tag">${toCountry}</span>
                    </div>
                </div>
            `;

            // Display direct connections
            if (directCables.length > 0) {
                html += `
                    <div class="path-section">
                        <div class="path-header">
                            <span class="path-type-badge path-type-direct">Direct</span>
                            ${directCables.length} Direct Connection${directCables.length !== 1 ? 's' : ''}
                        </div>
                        <div class="cables-list">
                `;

                directCables.forEach((cable, index) => {
                    html += generateCableHTML(cable, index, 'direct');
                });

                html += '</div></div>';
            }

            // Display multi-hop connections
            if (multiHopCables.length > 0) {
                // Group by path
                const pathGroups = new Map();
                multiHopCables.forEach(cable => {
                    const pathKey = cable.pathInfo.fullPath.join('➜');
                    if (!pathGroups.has(pathKey)) {
                        pathGroups.set(pathKey, []);
                    }
                    pathGroups.get(pathKey).push(cable);
                });

                pathGroups.forEach((pathCables, pathKey) => {
                    const fullPath = pathCables[0].pathInfo.fullPath;

                    html += `
                        <div class="path-section">
                            <div class="path-header">
                                <span class="path-type-badge path-type-multi-hop">Multi-hop</span>
                                Route via ${fullPath.length - 2} intermediate countr${fullPath.length - 2 !== 1 ? 'ies' : 'y'}
                            </div>
                            <div class="path-route">
                                <div class="path-route-label">Route:</div>
                                <div class="path-countries">
                                    ${fullPath.map((country, idx) => `
                                        <span class="path-country ${(idx === 0 || idx === fullPath.length - 1) ? 'highlight' : ''}">${country}</span>
                                        ${idx < fullPath.length - 1 ? '<span class="path-arrow" style="color: #3498db; font-weight: bold;">➜</span>' : ''}
                                    `).join('')}
                                </div>
                            </div>
                            <div class="path-cables">
                    `;

                    // Group cables by segment to avoid duplicate segment headers
                    const segmentGroups = new Map();
                    pathCables.forEach((cable) => {
                        const segmentKey = `${cable.pathInfo.segmentIndex}-${cable.pathInfo.segmentFrom}-${cable.pathInfo.segmentTo}`;
                        if (!segmentGroups.has(segmentKey)) {
                            segmentGroups.set(segmentKey, []);
                        }
                        segmentGroups.get(segmentKey).push(cable);
                    });

                    segmentGroups.forEach((segmentCables, segmentKey) => {
                        const segmentInfo = segmentCables[0].pathInfo;
                        html += `
                            <div class="path-segment">
                                <div class="segment-header">
                                    Segment ${segmentInfo.segmentIndex + 1}: ${segmentInfo.segmentFrom} ➜ ${segmentInfo.segmentTo}
                                </div>
                                <div class="segment-cables">
                        `;

                        segmentCables.forEach((cable, index) => {
                            html += generateCableHTML(cable, index, 'multi-hop');
                        });

                        html += `
                                </div>
                            </div>
                        `;
                    });
                    html += '</div></div>';
                });
            }
            resultsContainer.innerHTML = html;
        }

        function displayEnhancedSearchResults(searchResults, fromCountry, toCountry) {
            const resultsContainer = document.getElementById('cableResults');
            const directCables = searchResults.direct || [];
            const multiHopCables = searchResults.multiHop || [];
            const totalConnections = directCables.length + (multiHopCables.length > 0 ? 1 : 0); // Count multi-hop as one route

            if (totalConnections === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">🔎</div>
                        <div class="no-results-text">No connections found between ${fromCountry} and ${toCountry}</div>
                        <div class="no-results-suggestion">These countries may not be connected via submarine cables.</div>
                    </div>
                `;
                return;
            }

            // Use a global index counter to ensure unique colors across all cables
            let globalCableIndex = 0;

            let html = `
                <div class="search-results-header">
                    <div class="results-title">
                        <span class="results-icon">🔗</span>
                        Found ${totalConnections} connection type${totalConnections !== 1 ? 's' : ''}
                    </div>
                    <div class="results-route">
                        <span class="country-tag">${fromCountry}</span>
                        <span class="route-arrow">➜</span>
                        <span class="country-tag">${toCountry}</span>
                    </div>
                </div>
            `;
            // Display direct connections
            if (directCables.length > 0) {
                html += `
                    <div class="path-section">
                        <div class="path-header">
                            <span class="path-type-badge path-type-direct">Direct</span>
                            ${directCables.length} Direct Connection${directCables.length !== 1 ? 's' : ''}
                        </div>
                        <div class="cables-list">
                `;

                directCables.forEach((cable) => {
                    html += generateCableHTML(cable, globalCableIndex++, 'direct');
                });

                html += '</div></div>';
            }

            // Display multi-hop connections
            if (multiHopCables.length > 0) {
                // Group by path
                const pathGroups = new Map();
                multiHopCables.forEach(cable => {
                    const pathKey = cable.pathInfo.fullPath.join('➜');
                    if (!pathGroups.has(pathKey)) {
                        pathGroups.set(pathKey, []);
                    }
                    pathGroups.get(pathKey).push(cable);
                });

                pathGroups.forEach((pathCables, pathKey) => {
                    const fullPath = pathCables[0].pathInfo.fullPath;

                    html += `
                        <div class="path-section">
                            <div class="path-header">
                                <span class="path-type-badge path-type-multi-hop">Multi-hop</span>
                                Route via ${fullPath.length - 2} intermediate countr${fullPath.length - 2 !== 1 ? 'ies' : 'y'}
                            </div>
                            <div class="path-route">
                                <div class="path-route-label">Route:</div>
                                <div class="path-countries">
                                    ${fullPath.map((country, idx) => `
                                        <span class="path-country ${(idx === 0 || idx === fullPath.length - 1) ? 'highlight' : ''}">${country}</span>
                                        ${idx < fullPath.length - 1 ? '<span class="path-arrow">→</span>' : ''}
                                    `).join('')}
                                </div>
                            </div>
                            <div class="path-cables">
                    `;

                    // Group cables by segment to avoid duplicate segment headers
                    const segmentGroups = new Map();
                    pathCables.forEach((cable) => {
                        const segmentKey = `${cable.pathInfo.segmentIndex}-${cable.pathInfo.segmentFrom}-${cable.pathInfo.segmentTo}`;
                        if (!segmentGroups.has(segmentKey)) {
                            segmentGroups.set(segmentKey, []);
                        }
                        segmentGroups.get(segmentKey).push(cable);
                    });

                    segmentGroups.forEach((segmentCables, segmentKey) => {
                        const segmentInfo = segmentCables[0].pathInfo;
                        html += `
                            <div class="path-segment">
                                <div class="segment-header">
                                    Segment ${segmentInfo.segmentIndex + 1}: ${segmentInfo.segmentFrom} ➜ ${segmentInfo.segmentTo}
                                </div>
                                <div class="segment-cables">
                        `;

                        segmentCables.forEach((cable) => {
                            html += generateCableHTML(cable, globalCableIndex++, 'multi-hop');
                        });

                        html += `
                                </div>
                            </div>
                        `;
                    });

                    html += '</div></div>';
                });
            }
            resultsContainer.innerHTML = html;
        }

        function generateCableHTML(cable, index, pathType) {
            const cableData = cable.cableData || {};
            const cableName = cableData.name || cable.properties.name || 'Unknown Cable';
            const cableLength = cableData.length || 'Unknown';
            const rfsYear = cableData.rfs_year || 'Unknown';
            const owners = cableData.owners || 'Unknown';

            return `
                <div class="cable-result" onclick="selectIndividualCable('${cable.properties.id}', ${JSON.stringify(cable).replace(/"/g, '&quot;')})">
                    <div class="cable-name">
                        <span class="cable-color-indicator" style="background-color: ${getProfessionalCableColor(cable.properties.id, index)}"></span>
                        ${cableName}
                    </div>
                    <div class="cable-details">
                        ${cableLength !== 'Unknown' ? `Length: ${cableLength}` : ''}
                        ${rfsYear !== 'Unknown' ? ` • RFS: ${rfsYear}` : ''}
                        ${owners !== 'Unknown' ? `<br>Owners: ${owners}` : ''}
                    </div>
                    ${formatLandingPoints(cableData.landing_points)}
                </div>
            `;
        }

        async function loadCableData(cableId) {
            try {
                const response = await fetch(`../cable/${cableId}.json`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                return null;
            }
        }
        // Define European and African countries for routing scope
        function isEuropeanOrAfricanCountry(country) {
            const europeanCountries = new Set([
                'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
                'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
                'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
                'Norway', 'Poland', 'Portugal', 'Romania', 'San Marino', 'Serbia', 'Slovakia', 'Slovenia',
                'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City',
                // Include Turkey as it's part of European cable network
                'Turkey'
            ]);

            const africanCountries = new Set([
                'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon', 'Cape Verde',
                'Central African Republic', 'Chad', 'Comoros', 'Democratic Republic of the Congo', 'Republic of the Congo',
                'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea', 'Eswatini', 'Ethiopia',
                'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho', 'Liberia', 'Libya',
                'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco', 'Mozambique', 'Namibia',
                'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal', 'Seychelles', 'Sierra Leone',
                'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Tanzania', 'Togo', 'Tunisia', 'Uganda',
                'Zambia', 'Zimbabwe'
            ]);

            // Include Middle Eastern countries that are part of the Mediterranean/European cable network
            const middleEasternCountries = new Set([
                'Israel', 'Jordan', 'Lebanon', 'Syria', 'Palestine'
            ]);

            return europeanCountries.has(country) || africanCountries.has(country) || middleEasternCountries.has(country);
        }

        // Build country connectivity graph from all cable data
        async function buildCountryGraph() {
            countryGraph.clear();
            cableConnections.clear();
            console.log('Building country graph with Europe/Africa scope restriction');

            // Process only filtered cables visible on map to build the graph
            for (const cable of filteredCables) {
                try {
                    // Load individual cable data
                    const cableData = await loadCableData(cable.properties.id);
                    if (!cableData || !cableData.landing_points) continue;

                    const landingPoints = cableData.landing_points;
                    const allCountries = [...new Set(landingPoints.map(lp => lp.country))];

                    // Filter to only include European and African countries
                    const countries = allCountries.filter(country => isEuropeanOrAfricanCountry(country));

                    if (countries.length < 2) {
                        // Skip cables that don't connect at least 2 European/African countries
                        continue;
                    }

                    if (countries.includes('Cyprus') || countries.includes('Finland')) {
                        console.log(`Cable ${cable.properties.id} connects (filtered):`, countries);
                    }

                    // Add bidirectional connections between all countries in this cable
                    for (let i = 0; i < countries.length; i++) {
                        for (let j = i + 1; j < countries.length; j++) {
                            const country1 = countries[i];
                            const country2 = countries[j];

                            // Add to adjacency list
                            if (!countryGraph.has(country1)) countryGraph.set(country1, new Set());
                            if (!countryGraph.has(country2)) countryGraph.set(country2, new Set());

                            countryGraph.get(country1).add(country2);
                            countryGraph.get(country2).add(country1);

                            // Store cable connection info
                            const connectionKey1 = `${country1}➜${country2}`;
                            const connectionKey2 = `${country2}➜${country1}`;

                            if (!cableConnections.has(connectionKey1)) cableConnections.set(connectionKey1, []);
                            if (!cableConnections.has(connectionKey2)) cableConnections.set(connectionKey2, []);

                            const cableInfo = { ...cable, cableData };
                            cableConnections.get(connectionKey1).push(cableInfo);
                            cableConnections.get(connectionKey2).push(cableInfo);
                        }
                    }
                } catch (error) {
                    console.warn(`Failed to process cable ${cable.properties.id}:`, error);
                }
            }

            console.log(`Country graph built with ${countryGraph.size} European/African countries`);
            console.log('Countries in graph:', Array.from(countryGraph.keys()).sort());

            if (countryGraph.has('Cyprus')) {
                console.log('Cyprus connections:', Array.from(countryGraph.get('Cyprus')));
            }
            if (countryGraph.has('Finland')) {
                console.log('Finland connections:', Array.from(countryGraph.get('Finland')));
            }
        }
        // Find multi-hop path using BFS
        function findMultiHopPath(fromCountry, toCountry) {
            if (!countryGraph.has(fromCountry) || !countryGraph.has(toCountry)) {
                console.log(`Path finding failed: ${fromCountry} or ${toCountry} not in graph`);
                return null;
            }

            // Validate that both countries are European/African
            if (!isEuropeanOrAfricanCountry(fromCountry) || !isEuropeanOrAfricanCountry(toCountry)) {
                console.log(`Path finding failed: ${fromCountry} or ${toCountry} not in Europe/Africa scope`);
                return null;
            }

            const queue = [[fromCountry]];
            const visited = new Set([fromCountry]);

            while (queue.length > 0) {
                const path = queue.shift();
                const currentCountry = path[path.length - 1];

                if (currentCountry === toCountry) {
                    console.log(`Found path: ${path.join(' ➜ ')}`);
                    return path;
                }

                const neighbors = countryGraph.get(currentCountry) || new Set();
                for (const neighbor of neighbors) {
                    // Only consider neighbors that are European/African countries
                    if (!visited.has(neighbor) && isEuropeanOrAfricanCountry(neighbor)) {
                        visited.add(neighbor);
                        queue.push([...path, neighbor]);
                    }
                }
            }

            console.log(`No path found between ${fromCountry} and ${toCountry}`);
            return null;
        }
        // Get cables for a specific segment of the path
        function getCablesForSegment(fromCountry, toCountry) {
            const connectionKey = `${fromCountry}➜${toCountry}`;
            return cableConnections.get(connectionKey) || [];
        }

        // Enhanced findConnectingCables function with multi-hop support
        async function findConnectingCables(fromCountry, toCountry, searchType = 'direct') {
            if (searchType === 'direct') {
                return await findDirectConnections(fromCountry, toCountry);
            } else if (searchType === 'multi-hop') {
                return await findMultiHopConnections(fromCountry, toCountry);
            } else if (searchType === 'both') {
                const direct = await findDirectConnections(fromCountry, toCountry);
                const multiHop = await findMultiHopConnections(fromCountry, toCountry);
                return { direct, multiHop };
            }

            return [];
        }

        // Find direct connections (original functionality)
        async function findDirectConnections(fromCountry, toCountry) {
            const connectingCables = [];

            for (const cable of filteredCables) {
                try {
                    const cableData = await loadCableData(cable.properties.id);
                    if (!cableData || !cableData.landing_points) continue;

                    const countries = cableData.landing_points.map(lp => lp.country);
                    if (countries.includes(fromCountry) && countries.includes(toCountry)) {
                        connectingCables.push({
                            ...cable,
                            cableData,
                            pathType: 'direct'
                        });
                    }
                } catch (error) {
                    console.warn(`Failed to load cable data for ${cable.properties.id}:`, error);
                }
            }
            return connectingCables;
        }
        // Find multi-hop connections
        async function findMultiHopConnections(fromCountry, toCountry) {
            if (countryGraph.size === 0) {
                await buildCountryGraph();
            }
            const path = findMultiHopPath(fromCountry, toCountry);
            if (!path || path.length < 3) {
                return [];
            }
            // Get cables for each segment of the path
            const pathSegments = [];
            for (let i = 0; i < path.length - 1; i++) {
                const segmentFrom = path[i];
                const segmentTo = path[i + 1];
                const segmentCables = getCablesForSegment(segmentFrom, segmentTo);

                for (const cable of segmentCables) {
                    pathSegments.push({
                        ...cable,
                        pathType: 'multi-hop',
                        pathInfo: {
                            fullPath: path,
                            segmentIndex: i,
                            segmentFrom,
                            segmentTo,
                            isFirstSegment: i === 0,
                            isLastSegment: i === path.length - 2
                        }
                    });
                }
            }
            return pathSegments;
        }

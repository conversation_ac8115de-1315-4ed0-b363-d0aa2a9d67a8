<!DOCTYPE html>
<html>
<head>
    <title>Landing Point Highlighting Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
        }
        
        /* Import the landing point styles from the main CSS */
        .cable-landing-points {
            font-size: 11px;
            color: #5a6c7d;
            margin-top: 6px;
            padding-top: 6px;
            border-top: 1px solid #ecf0f1;
            line-height: 1.3;
        }

        .landing-points-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 2px;
        }

        .landing-points-route {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 4px;
        }

        .landing-point {
            background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
            white-space: nowrap;
            transition: all 0.2s ease;
        }

        /* Terminal landing point highlighting */
        .landing-point-terminal {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: 1px solid #2980b9;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
        }

        /* Country-specific highlighting */
        .landing-point-country-highlight {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: 1px solid #c0392b;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
        }

        /* Combined highlighting (terminal + country) */
        .landing-point-terminal.landing-point-country-highlight {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            border: 1px solid #8e44ad;
            box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3);
        }

        .route-arrow {
            color: #3498db;
            font-weight: bold;
            font-size: 10px;
            margin: 0 2px;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .control-group {
            margin-bottom: 10px;
        }

        label {
            display: inline-block;
            width: 200px;
            font-weight: 500;
        }

        input[type="checkbox"] {
            margin-right: 8px;
        }

        select {
            padding: 4px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Landing Point Highlighting Feature Test</h1>
        
        <div class="controls">
            <div class="control-group">
                <label>
                    <input type="checkbox" id="highlightTerminals"> Highlight Terminal Landing Points
                </label>
            </div>
            <div class="control-group">
                <label for="highlightCountry">Highlight Country:</label>
                <select id="highlightCountry">
                    <option value="">None</option>
                    <option value="Cameroon">Cameroon</option>
                    <option value="France">France</option>
                    <option value="South Africa">South Africa</option>
                    <option value="Portugal">Portugal</option>
                </select>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Test Case 1: Africa Coast to Europe (ACE) Cable</div>
            <div class="test-description">
                This cable connects multiple African countries to Europe. Terminal points should be highlighted in blue, 
                and the selected country should be highlighted in red. If a country is both terminal and selected, it shows in purple.
            </div>
            <div id="test1" class="cable-landing-points">
                <div class="landing-points-label">Landing Points:</div>
                <div class="landing-points-route">
                    <span class="landing-point" title="Cotonou, Benin">Cotonou, Benin</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Kribi, Cameroon">Kribi, Cameroon</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Abidjan, Côte d'Ivoire">Abidjan, Côte d'Ivoire</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Accra, Ghana">Accra, Ghana</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Lagos, Nigeria">Lagos, Nigeria</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Dakar, Senegal">Dakar, Senegal</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Penmarch, France">Penmarch, France</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Test Case 2: Short Cable (2 Landing Points)</div>
            <div class="test-description">
                For cables with only 2 landing points, both should be considered terminals.
            </div>
            <div id="test2" class="cable-landing-points">
                <div class="landing-points-label">Landing Points:</div>
                <div class="landing-points-route">
                    <span class="landing-point" title="Cape Town, South Africa">Cape Town, South Africa</span><span class="route-arrow">➜</span>
                    <span class="landing-point" title="Carcavelos, Portugal">Carcavelos, Portugal</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample landing points data
        const testData = {
            test1: [
                {name: "Cotonou, Benin", country: "Benin"},
                {name: "Kribi, Cameroon", country: "Cameroon"},
                {name: "Abidjan, Côte d'Ivoire", country: "Côte d'Ivoire"},
                {name: "Accra, Ghana", country: "Ghana"},
                {name: "Lagos, Nigeria", country: "Nigeria"},
                {name: "Dakar, Senegal", country: "Senegal"},
                {name: "Penmarch, France", country: "France"}
            ],
            test2: [
                {name: "Cape Town, South Africa", country: "South Africa"},
                {name: "Carcavelos, Portugal", country: "Portugal"}
            ]
        };

        function isTerminalLandingPoint(landingPoints, index) {
            if (!landingPoints || landingPoints.length <= 2) return true;
            return index === 0 || index === landingPoints.length - 1;
        }

        function updateHighlighting() {
            const highlightTerminals = document.getElementById('highlightTerminals').checked;
            const highlightCountry = document.getElementById('highlightCountry').value;

            Object.keys(testData).forEach(testId => {
                const container = document.getElementById(testId);
                const landingPoints = testData[testId];
                
                const landingPointsHtml = landingPoints.map((lp, index) => {
                    const locationName = lp.name;
                    const arrow = index < landingPoints.length - 1 ? '<span class="route-arrow">➜</span>' : '';
                    
                    let cssClasses = 'landing-point';
                    let title = locationName;
                    
                    const isTerminal = isTerminalLandingPoint(landingPoints, index);
                    if (highlightTerminals && isTerminal) {
                        cssClasses += ' landing-point-terminal';
                        title += ' (Terminal Landing Point)';
                    }
                    
                    if (highlightCountry && lp.country === highlightCountry) {
                        cssClasses += ' landing-point-country-highlight';
                        title += ` (Landing point of ${highlightCountry})`;
                    }
                    
                    return `<span class="${cssClasses}" title="${title}">${locationName}</span>${arrow}`;
                }).join('');

                const routeContainer = container.querySelector('.landing-points-route');
                routeContainer.innerHTML = landingPointsHtml;
            });
        }

        // Event listeners
        document.getElementById('highlightTerminals').addEventListener('change', updateHighlighting);
        document.getElementById('highlightCountry').addEventListener('change', updateHighlighting);

        // Initial update
        updateHighlighting();
    </script>
</body>
</html>
